services:
  postgres:
    restart: always
    image: postgres:latest
    ports:
      - "${HOST_DB_PORT}:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    env_file:
      - .env.production
    environment:
      POSTGRES_USER: ${PG_USERNAME}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
      POSTGRES_DB: ${PG_DATABASE}
    networks:
      - traefik-net

networks:
  traefik-net:
    external: true

volumes:
  pgdata:
