# /// script
# requires-python = ">=3.12"
# dependencies = [
#     "google-api-python-client",
#     "google-auth-httplib2",
#     "google-auth-oauthlib",
# ]
# ///

import io
import os.path
from datetime import datetime
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload

# Define the scopes and the name of the token file.
SCOPES = ["https://www.googleapis.com/auth/drive.file"]
TOKEN_FILE = "/home/<USER>/line-art-generator-v2//token.json"
PROJECT_FOLDER_NAME = 'coloraria'
MAX_BACKUPS = 3  # Maximum number of backups to keep


class GoogleDriveClient:
    """A class to interact with Google Drive."""

    def __init__(self, token_file: str = TOKEN_FILE, scopes: list = SCOPES, project_folder_name: str = PROJECT_FOLDER_NAME):
        self.token_file = token_file
        self.scopes = scopes
        self.creds = self._load_credentials()
        self.service = self._init_service()
        
        self.project_folder_name = project_folder_name
        self.folder = self._get_or_create_folder()

    def _load_credentials(self):
        """Load credentials from the token file."""
        creds = None
        if os.path.exists(self.token_file):
            creds = Credentials.from_authorized_user_file(self.token_file, self.scopes)
        return creds
    
    def _init_service(self):
        """Initialize the service with the given credentials."""
        if not self.creds:
            raise ValueError("Credentials are not loaded.")
        return build("drive", "v3", credentials=self.creds)

    def _get_or_create_folder(self):
        """Get the folder if it exists, create it otherwise."""
        files = self.list_files()
        for file in files['files']:
            if file['name'] == self.project_folder_name:
                return file
        return self.create_folder(self.project_folder_name)

    def list_files(self):
        """List all files in the Drive. 
        Returns a dictionaries with 'files' and 'nextPageToken' if there are more files. 
        files is a list of dictionaries with 'id' and 'name' keys.
        """
        results = self.service.files().list(
            pageSize=10,
            fields='files(id, name, mimeType, createdTime)',
            orderBy='createdTime desc'  # Sort by creation time, newest first
        ).execute()
        return results
    
    def list_files_in_folder(self, folder_id: str):
        """List all files in a folder.
        Returns a dictionaries with 'files' and 'nextPageToken' if there are more files.
        files is a list of dictionaries with 'id', 'name', 'mimeType', and 'createdTime' keys.
        """
        query = f"'{folder_id}' in parents"

        results = self.service.files().list(
            q=query,
            pageSize=10,  # Increased to handle more backups
            fields='files(id, name, mimeType, createdTime)',
            orderBy='createdTime desc'  # Sort by creation time, newest first
        ).execute()
        return results
    

    def create_folder(self, folder_name: str):
        """Create a folder in the Drive."""
        folder_metadata = {
            'name': folder_name,
            'mimeType': 'application/vnd.google-apps.folder'
        }
        folder = self.service.files().create(body=folder_metadata, fields='id, name').execute()
        return folder
    

    def upload_file(self, local_file_path: str, drive_file_name: str, parent_folder_id: str = None):
        """Upload a file to the Drive."""
        file_metadata = {
            'name': drive_file_name
        }
        if parent_folder_id:
            file_metadata['parents'] = [parent_folder_id]
        media = MediaFileUpload(local_file_path, resumable=True)
        file = self.service.files().create(
            body=file_metadata,
            media_body=media,
            fields='id'
        ).execute()
        return file
    
    def download_file(self, file_id: str, local_file_path: str):
        """Download a file from the Drive."""

        request = self.service.files().get_media(fileId=file_id)

        with io.FileIO(local_file_path, 'wb') as fh:
            downloader = MediaIoBaseDownload(fh, request)
            
            done = False
            print(f"Starting download...")
            while done is False:
                status, done = downloader.next_chunk()
                print(f"Download {int(status.progress() * 100)}%.")

        print(f"✅ Download complete! File saved to '{local_file_path}'")
    
    def delete_file(self, file_id: str):
        """Delete a file from the Drive."""
        self.service.files().delete(fileId=file_id).execute()

    def cleanup_old_backups(self, folder_id: str, max_backups: int = MAX_BACKUPS):
        """Keep only the most recent backups, delete older ones."""
        files_result = self.list_files_in_folder(folder_id)
        files = files_result.get('files', [])

        # Filter only backup files (assuming they start with 'db_backup_')
        backup_files = [f for f in files if f['name'].startswith('db_backup_')]

        # Files are already sorted by creation time (newest first) due to orderBy in list_files_in_folder
        if len(backup_files) > max_backups:
            files_to_delete = backup_files[max_backups:]
            print(f"Found {len(backup_files)} backups, keeping {max_backups}, deleting {len(files_to_delete)} old backups...")

            for file_to_delete in files_to_delete:
                print(f"Deleting old backup: {file_to_delete['name']} (created: {file_to_delete.get('createdTime', 'unknown')})")
                self.delete_file(file_to_delete['id'])
        else:
            print(f"Found {len(backup_files)} backups, no cleanup needed (max: {max_backups})")

        return len(files_to_delete) if len(backup_files) > max_backups else 0
    

def upload_backup_to_drive(local_backup_file: str):
    """Main backup workflow: upload backup, list files, cleanup old backups."""

    # Initialize Google Drive client
    google_drive_client = GoogleDriveClient()
    folder_id = google_drive_client.folder.get('id')

    print(f"📁 Using backup folder: {google_drive_client.project_folder_name} (ID: {folder_id})")

    # Check if backup file exists
    if not os.path.exists(local_backup_file):
        print(f"❌ Error: Backup file '{local_backup_file}' not found!")
        return False

    # Generate timestamped backup filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_filename = f"db_backup_{timestamp}.sql"

    print(f"📤 Uploading backup: {local_backup_file} -> {backup_filename}")

    # Upload the backup file
    try:
        result = google_drive_client.upload_file(local_backup_file, backup_filename, folder_id)
        print(f"✅ Upload successful! File ID: {result.get('id')}")
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        return False

    # List all files in the backup folder
    print(f"\n📋 Listing all backups in folder...")
    files_result = google_drive_client.list_files_in_folder(folder_id)
    files = files_result.get('files', [])

    backup_files = [f for f in files if f['name'].startswith('db_backup_')]
    print(f"Found {len(backup_files)} backup files:")
    for i, file in enumerate(backup_files, 1):
        created_time = file.get('createdTime', 'unknown')
        print(f"  {i}. {file['name']} (created: {created_time})")

    # Cleanup old backups (keep only the most recent MAX_BACKUPS)
    print(f"\n🧹 Cleaning up old backups (keeping max {MAX_BACKUPS})...")
    deleted_count = google_drive_client.cleanup_old_backups(folder_id, MAX_BACKUPS)

    if deleted_count > 0:
        print(f"✅ Cleanup complete! Deleted {deleted_count} old backup(s)")
    else:
        print("✅ No cleanup needed")

    # Final listing to show current state
    print(f"\n📋 Final backup listing:")
    files_result = google_drive_client.list_files_in_folder(folder_id)
    files = files_result.get('files', [])
    backup_files = [f for f in files if f['name'].startswith('db_backup_')]

    for i, file in enumerate(backup_files, 1):
        created_time = file.get('createdTime', 'unknown')
        print(f"  {i}. {file['name']} (created: {created_time})")

    print(f"\n🎉 Backup workflow completed successfully!")
    print(f"   - Uploaded: {backup_filename}")
    print(f"   - Total backups: {len(backup_files)}")
    print(f"   - Deleted old backups: {deleted_count}")

    return True


def main():
    """Example usage - replace with actual backup file path."""
    import sys
    if len(sys.argv) != 2:
        print("Usage: python upload_backup.py <backup_file_path>")
        sys.exit(1)

    backup_file_path = sys.argv[1]
    success = upload_backup_to_drive(backup_file_path)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
