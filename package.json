{"name": "line-art-generator-v2", "version": "1.0.0", "description": "Allow you to run line art generator, powered by payload 3.0 and nextjs", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev --turbo", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe:windows": "rmdir /s /q .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "lint:fix": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "prepare": "husky"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["next lint --fix --file"]}, "dependencies": {"@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@erase2d/fabric": "^1.1.7", "@hookform/resolvers": "^3.10.0", "@payloadcms/db-postgres": "3.49.1", "@payloadcms/next": "3.49.1", "@payloadcms/payload-cloud": "3.49.1", "@payloadcms/plugin-seo": "3.49.1", "@payloadcms/richtext-lexical": "3.49.1", "@payloadcms/storage-s3": "3.49.1", "@payloadcms/ui": "3.49.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.69.0", "@thumbmarkjs/thumbmarkjs": "^1.0.0", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.8.1", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "escape-html": "^1.0.3", "eventemitter3": "^5.0.1", "fabric": "^6.6.1", "form-data": "^4.0.2", "graphql": "^16.10.0", "http-status-codes": "^2.3.0", "husky": "^9.1.7", "idb-keyval": "^6.2.1", "input-otp": "^1.4.2", "jose": "^5.10.0", "lucide-react": "^0.464.0", "motion": "^12.16.0", "next": "15.4.4", "next-themes": "^0.4.6", "otpauth": "^9.4.0", "payload": "3.49.1", "posthog-js": "^1.259.0", "posthog-node": "^5.6.0", "qs": "^6.14.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-spinners": "^0.15.0", "sharp": "0.32.6", "sonner": "^2.0.3", "stripe": "^18.2.1", "tailwind-merge": "^2.6.0", "tailwind-scrollbar-hide": "^1.3.1", "tw-animate-css": "^1.3.4", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/escape-html": "^1.0.4", "@types/node": "^22.13.13", "@types/qs": "^6.9.18", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-config-next": "15.0.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.5", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.10", "typescript": "5.7.2"}, "engines": {"node": "^18.20.2 || >=20.9.0"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "packageManager": "pnpm@9.14.3+sha512.c0f53ee99477ed969b82b289ad011a5d16bf1623c957e7f29eabe8d0c00b574c29b8c7f54f6c67ee710c73f285c8154d07ce44b46fe2c0eeb476a90441bac371"}