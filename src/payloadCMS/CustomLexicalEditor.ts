import {
  lexicalEditor,
  FixedToolbarFeature,
  BlocksFeature,
  EXPERIMENTAL_TableFeature,
} from '@payloadcms/richtext-lexical'
import { DownloadLock } from '@/collections/Posts/blocks/DownloadLock'
import { FileDownloadLock } from '@/collections/Posts/blocks/FileDownloadLock'
import { YouTubeEmbed } from '@/collections/Posts/blocks/YouTubeEmbed'

export const CustomLexicalEditor = lexicalEditor({
  features: ({ defaultFeatures, rootFeatures }) => [
    ...defaultFeatures,
    FixedToolbarFeature(),
    BlocksFeature({
      blocks: [YouTubeEmbed, DownloadLock, FileDownloadLock],
    }),
    EXPERIMENTAL_TableFeature(),
  ],
})

export default CustomLexicalEditor
