// src/main.ts
import {
  S3Client as AWS,
  GetObjectCommand,
  PutObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3'
import { getSignedUrl, S3RequestPresigner } from '@aws-sdk/s3-request-presigner'

class S3Client {
  private s3: AWS
  private defaultBucket = process.env.AWS_BUCKET || ''

  constructor() {
    const clientConfig = {
      region: 'auto',
      signatureVersion: 'v4',
    }
    this.s3 = new AWS(clientConfig)
  }

  async generatePresignedUrl(
    objectKey: string,
    bucketName: string = this.defaultBucket,
    prefix: string = '',
    expiresIn: number = 36000,
  ): Promise<string> {
    try {
      const fullKey = prefix ? `${prefix}${objectKey}` : objectKey
      const command = new GetObjectCommand({ Bucket: bucketName, Key: fullKey })
      const url = getSignedUrl(this.s3, command, { expiresIn: expiresIn })

      return url
    } catch (error) {
      console.error('Error generating get_object URL:', error)
      throw error
    }
  }

  async generateUploadUrl(
    objectKey: string,
    bucketName: string = this.defaultBucket,
    prefix: string = '',
  ): Promise<string> {
    try {
      const fullKey = prefix ? `${prefix}${objectKey}` : objectKey
      const command = new PutObjectCommand({ Bucket: bucketName, Key: fullKey })
      const url = getSignedUrl(this.s3, command, { expiresIn: 300 })

      return url
    } catch (error) {
      console.error('Error generating upload URL:', error)
      throw error
    }
  }

  async deleteObject(
    objectKey: string,
    bucketName: string = this.defaultBucket,
    prefix: string = '',
  ): Promise<void> {
    try {
      const fullKey = prefix ? `${prefix}${objectKey}` : objectKey
      const command = new DeleteObjectCommand({ Bucket: bucketName, Key: fullKey })
      await this.s3.send(command)
    } catch (error) {
      console.error('Error deleting object:', error)
      throw error
    }
  }
}

export const s3Client = new S3Client()
