import type { CollectionConfig } from 'payload'
import isAdminOnly from '@/payloadCMS/roleaccess/isAdminOnly'

const FileDownloads: CollectionConfig = {
  slug: 'file-downloads',
  admin: {
    group: 'Content',
    useAsTitle: 'title',
  },
  access: {
    read: () => true,
    create: isAdminOnly,
    update: isAdminOnly,
    delete: isAdminOnly,
  },
  upload: {
    mimeTypes: ['application/zip', 'application/x-zip-compressed', 'multipart/x-zip'],
    filesRequiredOnCreate: true,
    disableLocalStorage: true,
  },
  fields: [
    {
      name: 'title',
      label: 'Title of the download',
      type: 'text',
      required: true,
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      hasMany: false,
      required: true,
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          if (req.user) {
            data.createdBy = req.user.id
            return data
          }
        }
        return data
      },
    ],
  },
}

export default FileDownloads
