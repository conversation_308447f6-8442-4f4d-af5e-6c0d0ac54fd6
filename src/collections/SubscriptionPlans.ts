import { CollectionConfig, CollectionBeforeOperationHook } from 'payload'
import { stripe } from '@/lib/stripe'

const USD = 'usd'

// Type-safe beforeOperation hook
const beforeOperationHook: CollectionBeforeOperationHook = async ({
  args,
  collection,
  context,
  operation,
  req,
}) => {
  try {
    // Only run this logic on create operations
    if (operation === 'create' && args.data) {
      // Create a product in Stripe
      const product = await stripe.products.create({
        name: args.data.name,
        description: args.data.description || undefined,
        default_price_data: {
          currency: USD,
          unit_amount: args.data.price, // Price in cents
          recurring: {
            interval: args.data.subscription_duration,
          },
        },
      })

      // Update the data with Stripe IDs
      args.data.id = product.id
      args.data.stripePriceId = product.default_price
      args.data.image_cap = args.data.monthlyCreditStipend
      // console.log('Created Stripe product:', product.id);
      // console.log('Created Stripe price:', price.id);
    } else if (operation === 'update' && args.data) {
      if (!args.data.id) {
        console.warn('No product ID found in the request data. Skipping Stripe update.')
        return
      }
      // Create a new price if the price or subscription duration has changed
      const newPrice = await stripe.prices.create({
        product: args.data.id,
        unit_amount: args.data.price, // Price in cents
        currency: USD,
        recurring: {
          interval: args.data.subscription_duration,
        },
      })

      // Update the product in Stripe
      await stripe.products.update(args.data.id, {
        name: args.data.name,
        description: args.data.description || undefined,
        default_price: newPrice.id,
      })

      // Update the data with the new Stripe price ID
      args.data.stripePriceId = newPrice.id
    }
    // on delete operation: no stripe call made since unable to remove product with price on Stripe side.
  } catch (error) {
    // console.error('Error creating Stripe subscription:', error);
    if (error instanceof Error) {
      throw new Error(`Error creating Stripe subscription plan: ${error.message}`)
    } else {
      throw new Error('Error creating Stripe subscription plan: Unknown error occurred')
    }
  }

  // Return the modified data to be saved
  return args
}

const SubscriptionPlan: CollectionConfig = {
  slug: 'subscription-plans',
  admin: {
    useAsTitle: 'name',
    group: 'Products',
  },
  hooks: {
    beforeOperation: [beforeOperationHook],
  },
  fields: [
    {
      name: 'id',
      label: 'Stripe Product ID',
      admin: {
        description: 'Refer to the Stripe Product ID and retrieve it',
        hidden: true,
      },
      type: 'text',
      required: true,
    },
    {
      name: 'name',
      label: 'Subscription Name:',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
    },
    {
      name: 'scope',
      label: 'Scope Assigned',
      type: 'relationship',
      relationTo: 'scopes',
      hasMany: false,
    },
    {
      name: 'stripePriceId',
      label: 'Stripe Price ID',
      admin: {
        description:
          'A product can have more than two prices (yearly / monthly). Denoted by the Stripe Price ID',
        hidden: true,
      },
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'price',
      label: 'Price (in cents)',
      type: 'number',
      required: true,
    },
    {
      name: 'monthlyCreditStipend',
      label: 'Monthly Credit Stipend',
      type: 'number',
      required: true,
    },
    {
      name: 'imageCap',
      label: 'Number of Images Allowed',
      type: 'number',
      required: true,
    },
    {
      name: 'subscription_duration',
      label: 'Subscription Duration',
      type: 'select',
      options: [
        { label: 'Daily', value: 'day' },
        { label: 'Weekly', value: 'week' },
        { label: 'Monthly', value: 'month' },
        { label: 'Yearly', value: 'year' },
      ],
      required: true,
    },
  ],
}

export default SubscriptionPlan
