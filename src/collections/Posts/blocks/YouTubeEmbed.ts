import { Block } from 'payload'

export const YouTubeEmbed: Block = {
  slug: 'youtubeEmbed',
  labels: {
    singular: 'Youtube Embed Block',
    plural: 'Youtube Embed Blocks',
  },
  fields: [
    {
      name: 'youtubeUrl',
      type: 'text',
      label: 'Youtube URL',
    },
    {
      name: 'aspectRatio',
      type: 'select',
      label: 'Aspect Ratio',
      hasMany: false,
      defaultValue: 'aspect_16_9',
      options: [
        { label: '16:9', value: 'aspect_16_9' },
        { label: '1:1', value: 'aspect_1_1' },
        { label: '9:16 (shorts)', value: 'aspect_9_16' },
      ],
    },
  ],
}
