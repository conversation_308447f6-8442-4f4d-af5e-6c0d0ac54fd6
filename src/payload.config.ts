// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { seoPlugin } from '@payloadcms/plugin-seo'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { s3Storage } from '@payloadcms/storage-s3'
import { buildConfig } from 'payload'
import sharp from 'sharp'
import path from 'path'
import { fileURLToPath } from 'url'

import Categories from './collections/Categories'
import Features from './collections/Features'
import FileDownloads from './collections/FileDownloads'
import GeneratedImages from './collections/GeneratedImages'
import { Media } from './collections/Media'
import Pages from './collections/Pages'
import Posts from './collections/Posts'
import PublicUsers from './collections/PublicUsers/PublicUsers'
import Scopes from './collections/Scopes'
import SubscriptionPlan from './collections/SubscriptionPlans'
import SubscriptionRecord from './collections/SubscriptionRecords/SubscriptionRecords'
import Tags from './collections/Tags'
import Users from './collections/Users'
import { migrations } from './migrations'
import { ReleaseCreditAnnualPlan } from './tasks/releaseCreditAnnualPlan'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)
const r2Domain = process.env.R2_PUBLIC_URL || ''

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname, 'payloadCMS'),
    },
  },
  collections: [
    Users,
    Media,
    PublicUsers,
    Posts,
    Pages,
    Categories,
    Tags,
    SubscriptionPlan,
    SubscriptionRecord,
    FileDownloads,
    GeneratedImages,
    Features,
    Scopes,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  routes: {
    api: '/la_api',
    admin: '/la_admin',
    graphQL: '/la_graphql',
    graphQLPlayground: '/la_graphql-playground',
  },
  db: postgresAdapter({
    pool: {
      connectionString: `postgresql://${process.env.PG_USERNAME}:${process.env.PG_PASSWORD}@${process.env.PG_HOST}:${process.env.PG_PORT}/${process.env.PG_DATABASE}`,
    },
    push: false,
    prodMigrations: process.env.NODE_ENV === 'production' ? migrations : undefined,
  }),
  graphQL: {
    disable: true,
  },
  sharp,
  telemetry: false,
  cookiePrefix: 'laAdmin',
  plugins: [
    s3Storage({
      collections: {
        media: {
          generateFileURL: ({ filename }: { filename: string }) => {
            return `${r2Domain}/${filename}`
          },
        },
      },
      bucket: process.env.R2_BLOG_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
        },
        region: 'auto',
        endpoint: process.env.AWS_ENDPOINT_URL || '',
      },
    }),
    s3Storage({
      collections: {
        'file-downloads': {
          signedDownloads: {
            expiresIn: 1800,
            shouldUseSignedURL: ({ collection, filename, req }) => {
              if (req.user?.role === 'admin' && req.user.collection === 'users') {
                return false
              }
              return filename.endsWith('.zip')
            },
          },
        },
      },
      bucket: process.env.R2_DOWNLOAD_BUCKET || '',
      config: {
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
        },
        region: 'auto',
        endpoint: process.env.AWS_ENDPOINT_URL || '',
      },
    }),
    payloadCloudPlugin(),
    seoPlugin({
      collections: ['posts', 'pages'],
      uploadsCollection: 'media',
      generateTitle: ({ doc }: { doc: { title: string } }) => `${doc.title} | ColorAria`,
      generateDescription: ({ doc }: { doc: { summary: string } }) => doc.summary,
    }),
  ],
  jobs: {
    autoRun: [
      {
        cron: '5 0 * * *',
        queue: 'nightly',
      },
    ],
    tasks: [ReleaseCreditAnnualPlan],
  },
})
