import { Layout } from '@/collections/Posts'
import { isObjectEmpty } from '../object'

interface ChildrenNode {
  mode: string
  text: string
  type: string
  detail: number
  format: number
  version: number
}

interface RichTextNode {
  type: string
  tag?: string
  text?: string
  children?: ChildrenNode[]
}

export function getDescription(richText: RichTextNode[], wordLimit: number = 100): string {
  let totalWords: string[] = []
  for (const node of richText) {
    if (node.children) {
      node.children.forEach((child) => {
        if (child.type === 'text') {
          totalWords.push(...child.text.trim().split(/\s+/))
        }
      })
    }
  }
  return totalWords.slice(0, wordLimit).join(' ')
}

export function getDescriptionFromLayout(layout: Layout[], wordLimit: number = 100): string {
  let totalWords: string[] = []
  console.log(layout)
  for (const block of layout) {
    if (
      block.blockType === 'content' &&
      block.richText &&
      block.richText.root &&
      block.richText.root.children
    ) {
      const richText = block.richText.root.children as RichTextNode[]
      for (const node of richText) {
        if (node.children) {
          node.children.forEach((child) => {
            if (child.type === 'text') {
              totalWords.push(...child.text.trim().split(/\s+/))
            }
          })
        }
      }
    }
  }
  return totalWords.slice(0, wordLimit).join(' ')
}

export function countTotalWords(nodes: RichTextNode[]): number {
  let total = 0

  function traverse(nodeList: RichTextNode[]) {
    for (const node of nodeList) {
      if (node.type === 'text' && typeof node.text === 'string') {
        total += node.text.trim().split(/\s+/).filter(Boolean).length
      }

      if (node.children?.length) {
        traverse(node.children)
      }
    }
  }

  traverse(nodes)
  return total
}

export function queryBuilder(queryParams: { [key: string]: unknown }) {
  let finalQuery = {} as Record<string, any>
  Object.keys(queryParams).forEach((key: string) => {
    if (queryParams[key]) {
      finalQuery[key] = queryParams[key]
    }
  })

  if (isObjectEmpty(finalQuery)) {
    return finalQuery
  }

  return undefined
}

export default queryBuilder
