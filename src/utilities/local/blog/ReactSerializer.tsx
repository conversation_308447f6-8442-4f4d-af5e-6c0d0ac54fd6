import type {
  DefaultNodeTypes,
  SerializedBlockNode,
  SerializedInlineBlockNode,
} from '@payloadcms/richtext-lexical'
import type { SerializedEditorState } from '@payloadcms/richtext-lexical/lexical'
import { RichText, type JSXConvertersFunction } from '@payloadcms/richtext-lexical/react'
import NextImage from 'next/image'
import Link from 'next/link'
import React, { JSX } from 'react'
import { components } from '@/app/(app)/_component/BlockRender/components'
import type { ContentType } from '@/app/(app)/_component/BlockRender/components/Content'
import type { DownloadLockType } from '@/app/(app)/_component/BlockRender/components/DownloadLock'
import { FileDownloadLock } from '@/app/(app)/_component/BlockRender/components/FileDownloadLock'
import type { YouTubeEmbedType } from '@/app/(app)/_component/BlockRender/components/YoutubeEmbed'
import Text from '@/app/(app)/_component/Text'
import Title, { HeaderLevel } from '@/app/(app)/_component/Title'
import { cn } from '@/lib/utils'
import { Media } from '@/payload-types'
import {
  IS_BOLD,
  IS_ITALIC,
  IS_STRIKETHROUGH,
  IS_UNDERLINE,
  IS_CODE,
  IS_SUBSCRIPT,
  IS_SUPERSCRIPT,
} from './RichTextNodeFormat'

//https://payloadcms.com/docs/rich-text/converting-jsx

const BlockComponent = ({ node }: { node: SerializedBlockNode }) => {
  const blockType = node.fields.blockType as keyof typeof components
  const Block: React.FC<any> | undefined = components[blockType]
  if (Block) {
    return <Block {...node.fields} />
  }
}

type NodeTypes =
  | DefaultNodeTypes
  | SerializedBlockNode<ContentType | DownloadLockType | YouTubeEmbedType | FileDownloadLock>
  | SerializedInlineBlockNode

const jsxConverters: JSXConvertersFunction<NodeTypes> = ({ defaultConverters }) => ({
  ...defaultConverters,
  paragraph: ({ node, converters, nodesToJSX }) => {
    const hasChildren = node.children && node.children.length > 0
    let text = (
      <Text
        as="p"
        className="[&>*:not(ul):not(ol):not(li)]:mt-6 mb-4 leading-relaxed text-lg font-normal tracking-normal"
      >
        {hasChildren ? nodesToJSX({ parent: node, nodes: node.children, converters }) : <br />}
      </Text>
    )

    return text
  },

  text: ({ node }) => {
    let text = <>{node.text}</>
    if (node.format & IS_BOLD) {
      text = (
        <Text as="strong" size="lg" variant="emphasis">
          {text}
        </Text>
      )
    }
    if (node.format & IS_ITALIC) {
      text = (
        <Text as="em" size="lg" className="italic">
          {text}
        </Text>
      )
    }
    if (node.format & IS_STRIKETHROUGH) {
      text = (
        <Text size="lg" className="line-through">
          {text}
        </Text>
      )
    }
    if (node.format & IS_UNDERLINE) {
      text = (
        <Text size="lg" className="underline">
          {text}
        </Text>
      )
    }
    if (node.format & IS_CODE) {
      text = (
        <Text as="code" size="lg" className="font-mono bg-gray-100 px-1 py-0.5 rounded">
          {text}
        </Text>
      )
    }
    if (node.format & IS_SUBSCRIPT) {
      text = (
        <Text as="sub" size="lg" className="text-sm">
          {text}
        </Text>
      )
    }
    if (node.format & IS_SUPERSCRIPT) {
      text = (
        <Text as="sup" size="lg" className="text-sm">
          {text}
        </Text>
      )
    }

    return text
  },

  // Headings
  heading: ({ node, converters, nodesToJSX }) => {
    const Tag = node.tag as HeaderLevel
    return (
      <Title level={Tag} className="mt-6 mb-6">
        {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
      </Title>
    )
  },

  list: ({ node, converters, nodesToJSX }) => {
    const Tag = node.tag as 'ul' | 'ol'
    const isOrdered = Tag === 'ol'

    return (
      <Tag className={cn(isOrdered ? 'custom-ordered-list' : 'custom-unordered-list', 'mb-1')}>
        {nodesToJSX({ parent: node, nodes: node.children, converters })}
      </Tag>
    )
  },

  listitem: ({ node, converters, nodesToJSX }) => {
    return <li>{nodesToJSX({ parent: node, nodes: node.children, converters })}</li>
  },

  // // Blockquote
  quote: ({ node, converters, nodesToJSX }) => (
    <Text as="blockquote" size="lg" variant="quote" className={cn('border-l-4 pl-4 mt-6 mb-6')}>
      {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
    </Text>
  ),

  // // Link
  link: ({ node, converters, nodesToJSX }) => {
    const { url, newTab, nofollow, rel, linkType } = node.fields || {}
    console.log(node.fields)
    const fullRel = `${rel ?? ''} ${nofollow ? ' nofollow' : ''}`.trim()
    if (linkType === 'custom') {
      return (
        <Link
          href={url ?? ''}
          target={newTab ? '_blank' : undefined}
          rel={fullRel}
          className="text-accent2 hover:text-accent2-lighter underline"
        >
          {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
        </Link>
      )
    } else if (linkType === 'internal') {
      //We assume it's BLOG
      const { value, relationTo } = node.fields.doc!
      if (typeof value !== 'object') {
        throw new Error('Expected value to be an object')
      }
      const slug = value.slug
      if (relationTo === 'posts') {
        return (
          <Link
            href={`/blog/${slug}`}
            target={newTab ? '_blank' : undefined}
            rel={fullRel}
            className="text-accent2 hover:text-accent2-lighter underline"
          >
            {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
          </Link>
        )
      } else {
        return `/${slug}`
      }
    } else {
      return (
        <Link
          href={url ?? ''}
          target={newTab ? '_blank' : undefined}
          rel={fullRel}
          className="text-accent2 hover:text-accent2-lighter underline"
        >
          {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
        </Link>
      )
    }
  },

  // // Upload (images)
  upload: ({ node }) => {
    const { url, alt, width, height } = node.value as Media
    return (
      <NextImage
        src={url ?? ''}
        alt={alt}
        width={width ?? 200}
        height={height ?? 200}
        className="mb-6 mt-4 rounded-2xl"
      />
    )
  },

  // // Experimental Table
  table: ({ node, converters, nodesToJSX }) => (
    <table className="border-collapse border border-gray-400 w-full my-6 mt-6">
      <tbody>{nodesToJSX({ parent: node, nodes: node.children, converters: converters })}</tbody>
    </table>
  ),

  tableRow: ({ node, converters, nodesToJSX }) => (
    <tr>{nodesToJSX({ parent: node, nodes: node.children, converters: converters })}</tr>
  ),
  tableCell: ({ node, converters, nodesToJSX }) => {
    const isHeader = node.tag === 'th'
    const CellTag = isHeader ? 'th' : 'td'
    return (
      <CellTag className="border border-gray-400 px-2 py-1 align-top">
        {nodesToJSX({ parent: node, nodes: node.children, converters: converters })}
      </CellTag>
    )
  },
  blocks: {
    content: ({ node }) => <BlockComponent node={node} />,
    downloadLock: ({ node }) => <BlockComponent node={node} />,
    youtubeEmbed: ({ node }) => <BlockComponent node={node} />,
    fileDownloadLock: ({ node }) => <BlockComponent node={node} />,
  },
})

const SerializeComponent = ({ data }: { data: SerializedEditorState }) => {
  return (
    <div className="">
      <RichText converters={jsxConverters} data={data} />
    </div>
  )
}

export default SerializeComponent
