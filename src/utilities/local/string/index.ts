export function capitalizeAndReplace(input: string | null | undefined, separator = '_'): string {
  if (input == null) {
    return ''
  }

  const escapedSeparator = separator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

  // return input
  //   .replace(/_/g, ' ') // Replace underscores with spaces
  //   .replace(/^\w/, (c) => c.toUpperCase()) // Capitalize the first letter
  return input
    .replace(new RegExp(escapedSeparator, 'g'), ' ') // Replace the separator with spaces
    .replace(/^\w/, (c) => c.toUpperCase()) // Capitalize
}

export function capitalizeFirstCharWordAndReplace(
  input: string | null | undefined,
  separator = '_',
): string {
  if (input == null) {
    return ''
  }

  const escapedSeparator = separator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

  return input
    .replace(new RegExp(escapedSeparator, 'g'), ' ')
    .toLowerCase()
    .replace(/\b\w/g, (c) => c.toUpperCase())
}

export function capitalizeAllLetters(input: string | null | undefined): string {
  if (input == null) {
    return ''
  }
  return input.toUpperCase()
}
