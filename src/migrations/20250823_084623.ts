import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE "posts_blocks_file_download_lock" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar DEFAULT 'Your download is waiting!',
  	"description" varchar,
  	"file_download_id" integer,
  	"block_name" varchar
  );
  
  CREATE TABLE "_posts_v_blocks_file_download_lock" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar DEFAULT 'Your download is waiting!',
  	"description" varchar,
  	"file_download_id" integer,
  	"_uuid" varchar,
  	"block_name" varchar
  );
  
  CREATE TABLE "pages_blocks_file_download_lock" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"_path" text NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"title" varchar DEFAULT 'Your download is waiting!',
  	"description" varchar,
  	"file_download_id" integer NOT NULL,
  	"block_name" varchar
  );
  
  ALTER TABLE "posts_blocks_file_download_lock" ADD CONSTRAINT "posts_blocks_file_download_lock_file_download_id_file_downloads_id_fk" FOREIGN KEY ("file_download_id") REFERENCES "public"."file_downloads"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "posts_blocks_file_download_lock" ADD CONSTRAINT "posts_blocks_file_download_lock_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."posts"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "_posts_v_blocks_file_download_lock" ADD CONSTRAINT "_posts_v_blocks_file_download_lock_file_download_id_file_downloads_id_fk" FOREIGN KEY ("file_download_id") REFERENCES "public"."file_downloads"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "_posts_v_blocks_file_download_lock" ADD CONSTRAINT "_posts_v_blocks_file_download_lock_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_posts_v"("id") ON DELETE cascade ON UPDATE no action;
  ALTER TABLE "pages_blocks_file_download_lock" ADD CONSTRAINT "pages_blocks_file_download_lock_file_download_id_file_downloads_id_fk" FOREIGN KEY ("file_download_id") REFERENCES "public"."file_downloads"("id") ON DELETE set null ON UPDATE no action;
  ALTER TABLE "pages_blocks_file_download_lock" ADD CONSTRAINT "pages_blocks_file_download_lock_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "posts_blocks_file_download_lock_order_idx" ON "posts_blocks_file_download_lock" USING btree ("_order");
  CREATE INDEX "posts_blocks_file_download_lock_parent_id_idx" ON "posts_blocks_file_download_lock" USING btree ("_parent_id");
  CREATE INDEX "posts_blocks_file_download_lock_path_idx" ON "posts_blocks_file_download_lock" USING btree ("_path");
  CREATE INDEX "posts_blocks_file_download_lock_file_download_idx" ON "posts_blocks_file_download_lock" USING btree ("file_download_id");
  CREATE INDEX "_posts_v_blocks_file_download_lock_order_idx" ON "_posts_v_blocks_file_download_lock" USING btree ("_order");
  CREATE INDEX "_posts_v_blocks_file_download_lock_parent_id_idx" ON "_posts_v_blocks_file_download_lock" USING btree ("_parent_id");
  CREATE INDEX "_posts_v_blocks_file_download_lock_path_idx" ON "_posts_v_blocks_file_download_lock" USING btree ("_path");
  CREATE INDEX "_posts_v_blocks_file_download_lock_file_download_idx" ON "_posts_v_blocks_file_download_lock" USING btree ("file_download_id");
  CREATE INDEX "pages_blocks_file_download_lock_order_idx" ON "pages_blocks_file_download_lock" USING btree ("_order");
  CREATE INDEX "pages_blocks_file_download_lock_parent_id_idx" ON "pages_blocks_file_download_lock" USING btree ("_parent_id");
  CREATE INDEX "pages_blocks_file_download_lock_path_idx" ON "pages_blocks_file_download_lock" USING btree ("_path");
  CREATE INDEX "pages_blocks_file_download_lock_file_download_idx" ON "pages_blocks_file_download_lock" USING btree ("file_download_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "posts_blocks_file_download_lock" CASCADE;
  DROP TABLE "_posts_v_blocks_file_download_lock" CASCADE;
  DROP TABLE "pages_blocks_file_download_lock" CASCADE;`)
}
