import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE "file_downloads" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"title" varchar NOT NULL,
  	"created_by_id" integer NOT NULL,
  	"updated_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"created_at" timestamp(3) with time zone DEFAULT now() NOT NULL,
  	"url" varchar,
  	"thumbnail_u_r_l" varchar,
  	"filename" varchar,
  	"mime_type" varchar,
  	"filesize" numeric,
  	"width" numeric,
  	"height" numeric,
  	"focal_x" numeric,
  	"focal_y" numeric
  );
  
  ALTER TABLE "payload_locked_documents_rels" ADD COLUMN "file_downloads_id" integer;
  ALTER TABLE "file_downloads" ADD CONSTRAINT "file_downloads_created_by_id_users_id_fk" FOREIGN KEY ("created_by_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;
  CREATE INDEX "file_downloads_created_by_idx" ON "file_downloads" USING btree ("created_by_id");
  CREATE INDEX "file_downloads_updated_at_idx" ON "file_downloads" USING btree ("updated_at");
  CREATE INDEX "file_downloads_created_at_idx" ON "file_downloads" USING btree ("created_at");
  CREATE UNIQUE INDEX "file_downloads_filename_idx" ON "file_downloads" USING btree ("filename");
  ALTER TABLE "payload_locked_documents_rels" ADD CONSTRAINT "payload_locked_documents_rels_file_downloads_fk" FOREIGN KEY ("file_downloads_id") REFERENCES "public"."file_downloads"("id") ON DELETE cascade ON UPDATE no action;
  CREATE INDEX "payload_locked_documents_rels_file_downloads_id_idx" ON "payload_locked_documents_rels" USING btree ("file_downloads_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "file_downloads" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "file_downloads" CASCADE;
  ALTER TABLE "payload_locked_documents_rels" DROP CONSTRAINT "payload_locked_documents_rels_file_downloads_fk";
  
  DROP INDEX "payload_locked_documents_rels_file_downloads_id_idx";
  ALTER TABLE "payload_locked_documents_rels" DROP COLUMN "file_downloads_id";`)
}
