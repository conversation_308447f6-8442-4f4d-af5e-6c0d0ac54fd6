import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_posts_blocks_youtube_embed_aspect_ratio" AS ENUM('aspect_16_9', 'aspect_1_1', 'aspect_9_16');
  CREATE TYPE "public"."enum__posts_v_blocks_youtube_embed_aspect_ratio" AS ENUM('aspect_16_9', 'aspect_1_1', 'aspect_9_16');
  CREATE TYPE "public"."enum_pages_blocks_youtube_embed_aspect_ratio" AS ENUM('aspect_16_9', 'aspect_1_1', 'aspect_9_16');
  ALTER TABLE "posts_blocks_youtube_embed" ADD COLUMN "aspect_ratio" "enum_posts_blocks_youtube_embed_aspect_ratio" DEFAULT 'aspect_16_9';
  ALTER TABLE "_posts_v_blocks_youtube_embed" ADD COLUMN "aspect_ratio" "enum__posts_v_blocks_youtube_embed_aspect_ratio" DEFAULT 'aspect_16_9';
  ALTER TABLE "pages_blocks_youtube_embed" ADD COLUMN "aspect_ratio" "enum_pages_blocks_youtube_embed_aspect_ratio" DEFAULT 'aspect_16_9';`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "posts_blocks_youtube_embed" DROP COLUMN "aspect_ratio";
  ALTER TABLE "_posts_v_blocks_youtube_embed" DROP COLUMN "aspect_ratio";
  ALTER TABLE "pages_blocks_youtube_embed" DROP COLUMN "aspect_ratio";
  DROP TYPE "public"."enum_posts_blocks_youtube_embed_aspect_ratio";
  DROP TYPE "public"."enum__posts_v_blocks_youtube_embed_aspect_ratio";
  DROP TYPE "public"."enum_pages_blocks_youtube_embed_aspect_ratio";`)
}
