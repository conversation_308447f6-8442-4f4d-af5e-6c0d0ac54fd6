import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
    ALTER TABLE "subscription_plans" ADD COLUMN "image_cap" numeric;
  `)

  await db.execute(sql`
    UPDATE "subscription_plans" SET "image_cap" = "monthly_credit_stipend";
  `)

  await db.execute(sql`
    ALTER TABLE "subscription_plans" ALTER COLUMN "image_cap" SET NOT NULL;
  `)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "subscription_plans" DROP COLUMN "image_cap";`)
}
