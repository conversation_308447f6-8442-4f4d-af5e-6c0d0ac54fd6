import { NextRequest } from 'next/server'
import { s3Client } from '@/lib/s3-client'
import { errorHandler } from '../../_backend/common/exception/errorHandler'

export interface FileDownloadRequest {
  filename: string
}

export interface FileDownloadResponse {
  url: string
}

export async function POST(request: NextRequest) {
  try {
    const { filename } = (await request.json()) as FileDownloadRequest
    const presignedUrl = await s3Client.generatePresignedUrl(
      filename,
      process.env.R2_DOWNLOAD_BUCKET || '',
      undefined,
      3600,
    )
    return new Response(
      JSON.stringify({ message: 'Endpoint successfully generated', data: { url: presignedUrl } }),
    )
  } catch (error) {
    return errorHandler(error)
  }
}
