import { StatusCodes } from 'http-status-codes'
import { getPayload } from 'payload'
import <PERSON><PERSON><PERSON> from '@/app/(app)/_backend/common/dao/FeaturesDAO'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import User<PERSON><PERSON> from '@/app/(app)/_backend/common/dao/UserDAO'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import FeatureScopeService from '@/app/(app)/_backend/common/service/FeatureScopeService'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import UnsplashService from '@/app/(app)/_backend/common/service/UnsplashService'
import UserService from '@/app/(app)/_backend/common/service/UserService'
import PostHogClient from '@/lib/posthog'
import { FeaturesType } from '@/utilities/local/enums'
import config from '@payload-config'

// type Quality = 'LOW_QUALITY' | 'HIGH_QUALITY'
type I2IAspectRatio = '1:1' | '2:3' | '3:2'

interface ImageGenerationRequestI2I {
  imageBase64: string
  aspectRatio: I2IAspectRatio
  numberOfImages: number
  unsplashSrc?: string
}

export interface ImageGenerationResponseI2I {
  remainingCredits: number
}

const mandatoryFeatures = [
  FeaturesType.IMAGE_TO_COLOR,
  FeaturesType.IMAGE_TO_COLOR_IMAGE_REPETITION,
]

export async function POST(request: Request) {
  try {
    const { imageBase64, aspectRatio, numberOfImages, unsplashSrc } =
      (await request.json()) as ImageGenerationRequestI2I

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)
    const payload = await getPayload({ config })

    const userDAO = new UserDAO(payload)
    const featuresDAO = new FeaturesDAO(payload)
    const userService = new UserService(userDAO)
    const featureScopeService = new FeatureScopeService(userDAO, featuresDAO)

    const costTabulation = await featureScopeService.costTabulation(user.id, mandatoryFeatures)
    const remainingCredits = (await userService.deductCredit(user.id, costTabulation)).freeCredits

    //TODO: DETERMINE THE QUALITY FROM USER'S MEMBERSHIP
    const quality = 'HIGH_QUALITY'

    console.log(unsplashSrc)

    if (unsplashSrc != '' && unsplashSrc != null) {
      const unsplashService = new UnsplashService()
      unsplashService.attributionLink(unsplashSrc) // this is event-based fire and forget, no response required.
    }

    const generatedImageDAO = new GeneratedImageDAO(payload)
    const imageService = new ImageService(generatedImageDAO)

    // Move all validations to API level (synchronous)
    if (!imageBase64) throw new HTTPException('Image is required', StatusCodes.BAD_REQUEST)

    if (numberOfImages > imageService.IMAGE_REPETITION_CAP || numberOfImages <= 0) {
      throw new HTTPException(
        `Number of images must be less than or equal to ${imageService.IMAGE_REPETITION_CAP}`,
        StatusCodes.BAD_REQUEST,
      )
    }

    if (await imageService.exceedsImageCapacity(user.id, numberOfImages)) {
      throw new HTTPException(
        'Image capacity exceeded, upgrade your plan or remove existing images.',
        StatusCodes.PAYMENT_REQUIRED,
      )
    }

    if (await imageService.hasGeneratingTask(user.id)) {
      throw new HTTPException(
        'A generation task is already in progress for this user.',
        StatusCodes.TOO_MANY_REQUESTS,
      )
    }

    // Start async generation (no await) - pass refund function for potential credit refund
    const refundCredits = async () => {
      await userService.addCredit(user.id, costTabulation)
    }
    imageService.imageToImage(
      imageBase64,
      aspectRatio,
      quality,
      numberOfImages,
      user.id,
      refundCredits,
    )

    // Track image-to-image generation event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'image_to_image_generation',
        distinctId: user.id,
        properties: {
          aspectRatio,
          quality,
          numberOfImages,
          hasUnsplashSource: !!unsplashSrc,
          remainingCredits,
        },
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    // note: not returning the image
    return new Response(
      JSON.stringify({
        message: 'Generating images...',
        data: {
          remainingCredits,
        },
      }),
      {
        status: 202,
      },
    )
  } catch (error) {
    return errorHandler(error)
  }
}
