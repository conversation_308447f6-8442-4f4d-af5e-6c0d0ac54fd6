import { StatusCodes } from 'http-status-codes'
import { getPayload } from 'payload'
import GeneratedImageDAO from '@/app/(app)/_backend/common/dao/GeneratedImageDAO'
import { HTTPException } from '@/app/(app)/_backend/common/exception'
import { errorHandler } from '@/app/(app)/_backend/common/exception/errorHandler'
import ImageService from '@/app/(app)/_backend/common/service/ImageService'
import PostHogClient from '@/lib/posthog'
import config from '@payload-config'

interface ImageContrastRequest {
  b64: string
}

export interface ImageContrastResponse {
  src: string
  width: number
  height: number
}

export async function POST(request: Request) {
  try {
    const { b64 } = (await request.json()) as ImageContrastRequest

    const userHeader = request.headers.get('x-user')
    const user = JSON.parse(userHeader!)

    const payload = await getPayload({ config })
    const generatedImageDAO = new GeneratedImageDAO(payload)
    const imageService = new ImageService(generatedImageDAO)

    // Check image capacity before processing
    if (await imageService.exceedsImageCapacity(user.id, 1)) {
      throw new HTTPException(
        'Image capacity exceeded, upgrade your plan or remove existing images.',
        StatusCodes.PAYMENT_REQUIRED,
      )
    }

    const imageContrasted = await imageService.contrastImage(b64, user.id)

    // Track image contrast adjustment event
    try {
      const posthog = PostHogClient()
      posthog.capture({
        event: 'image_contrast_adjustment',
        distinctId: user.id,
        properties: {
          imageWidth: imageContrasted.width,
          imageHeight: imageContrasted.height,
        },
      })
    } catch (error) {
      console.error('PostHog tracking error:', error)
    }

    return new Response(
      JSON.stringify({
        message: 'Image contrast has been successfully adjusted!',
        data: imageContrasted,
      }),
    )
  } catch (error) {
    errorHandler(error)
  }
}
