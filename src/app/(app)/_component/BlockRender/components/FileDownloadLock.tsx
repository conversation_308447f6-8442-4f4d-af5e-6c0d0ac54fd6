'use client'

import { useQuery } from '@tanstack/react-query'
import { Download, DownloadCloud, Lock } from 'lucide-react'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { PropagateLoader } from 'react-spinners'
import { _verifyJWT } from '@/app/(app)/_backend/common/utils/auth'
import FlexContainer, {
  AlignItems,
  FlexDirection,
  JustifyContent,
} from '@/app/(app)/_cssComp/FlexContainer'
import { fetchPresignedUrl } from '@/app/(app)/_localApi/fileDownload'
import { FileDownload } from '@/payload-types'
import { Button } from '../../Button'
import Card from '../../Card/CardSolo'
import Text from '../../Text'
import Title, { HeaderLevel } from '../../Title'

export type FileDownloadLock = {
  blockType: 'fileDownloadLock'
  blockName?: string | null
  title?: string | null
  description?: string | null
  fileDownload?: FileDownload
}

function FileDownloadLock(props: FileDownloadLock) {
  const {
    title = 'Thank you for your support!',
    description = 'Download now!',
    fileDownload,
  } = props

  const [isHydrated, setIsHydrated] = useState(false)
  const [presignedUrl, setPresignedUrl] = useState<string | null>(null)

  const result = useQuery({
    queryKey: ['presignedUrl', fileDownload?.filename],
    queryFn: () => fetchPresignedUrl(fileDownload?.filename || ''),
    retry: false,
    refetchOnWindowFocus: false,
    enabled: isHydrated,
  })

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  useEffect(() => {
    if (result.data?.success === true) {
      setPresignedUrl(result.data.data.url)
    }
  }, [result.data])

  const fileDownloadComponent = presignedUrl ? (
    <Card className="bg-green-100 mt-4 mb-4">
      <FlexContainer
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
        justify={JustifyContent.CENTER}
        className="w-full h-full gap-2"
      >
        <Title level={HeaderLevel.H4}>
          <FlexContainer className="gap-2" direction={FlexDirection.ROW}>
            <DownloadCloud /> {title} <DownloadCloud />
          </FlexContainer>
        </Title>
        <Text variant="description">{description}</Text>
        {fileDownload?.url && (
          <Link href={presignedUrl} target="_blank" rel="noopener noreferrer">
            <Button variant="positive">
              <Download /> Download Now!
            </Button>
          </Link>
        )}
      </FlexContainer>
    </Card>
  ) : (
    <Card className="bg-red-100 mt-4 mb-4">
      <FlexContainer
        direction={FlexDirection.COL}
        align={AlignItems.CENTER}
        justify={JustifyContent.CENTER}
        className="w-full h-full gap-2"
      >
        <Title level={HeaderLevel.H4} className="text-center">
          <FlexContainer className="gap-2" direction={FlexDirection.ROW} wrap={false}>
            <Lock /> {`Looks like you're not logged in!`} <Lock />
          </FlexContainer>
        </Title>
        <Text variant="description" className="mb-2 text-center">
          {`Log in or create an account to access this content.`}
        </Text>
        <FlexContainer
          className="w-full gap-2"
          direction={FlexDirection.ROW}
          justify={JustifyContent.CENTER}
          align={AlignItems.CENTER}
        >
          <Link href="/login">
            <Button variant="secondary">Login</Button>
          </Link>
          <Link href="/register">
            <Button variant="emphasis">Sign Up</Button>
          </Link>
        </FlexContainer>
      </FlexContainer>
    </Card>
  )

  if (!isHydrated) {
    return (
      <FlexContainer align={AlignItems.CENTER} justify={JustifyContent.CENTER} className="w-full">
        <PropagateLoader size={16} />
      </FlexContainer>
    )
  }

  return result.isLoading ? (
    <FlexContainer align={AlignItems.CENTER} justify={JustifyContent.CENTER} className="w-full">
      <PropagateLoader size={16} />
    </FlexContainer>
  ) : (
    fileDownloadComponent
  )

  // try {
  //   return (
  //     <Card className="bg-green-100 mt-4 mb-4">
  //       <FlexContainer
  //         direction={FlexDirection.COL}
  //         align={AlignItems.CENTER}
  //         justify={JustifyContent.CENTER}
  //         className="w-full h-full gap-2"
  //       >
  //         <Title level={HeaderLevel.H4}>
  //           <FlexContainer className="gap-2" direction={FlexDirection.ROW}>
  //             <DownloadCloud /> {title} <DownloadCloud />
  //           </FlexContainer>
  //         </Title>
  //         <Text variant="description">{description}</Text>
  //         {fileDownload?.url && (
  //           <Link href={presignedUrl} target="_blank" rel="noopener noreferrer">
  //             <Button variant="positive">
  //               <Download /> Download Now!
  //             </Button>
  //           </Link>
  //         )}
  //       </FlexContainer>
  //     </Card>
  //   )
  // } catch (error) {
  //   return (
  //     <Card className="bg-red-100 mt-4 mb-4">
  //       <FlexContainer
  //         direction={FlexDirection.COL}
  //         align={AlignItems.CENTER}
  //         justify={JustifyContent.CENTER}
  //         className="w-full h-full gap-2"
  //       >
  //         <Title level={HeaderLevel.H4} className="text-center">
  //           <FlexContainer className="gap-2" direction={FlexDirection.ROW} wrap={false}>
  //             <Lock /> {`Looks like you're not logged in!`} <Lock />
  //           </FlexContainer>
  //         </Title>
  //         <Text variant="description" className="mb-2 text-center">
  //           {`Log in or create an account to access this content.`}
  //         </Text>
  //         <FlexContainer
  //           className="w-full gap-2"
  //           direction={FlexDirection.ROW}
  //           justify={JustifyContent.CENTER}
  //           align={AlignItems.CENTER}
  //         >
  //           <Link href="/login">
  //             <Button variant="secondary">Login</Button>
  //           </Link>
  //           <Link href="/register">
  //             <Button variant="emphasis">Sign Up</Button>
  //           </Link>
  //         </FlexContainer>
  //       </FlexContainer>
  //     </Card>
  //   )
  // }
}

export default FileDownloadLock
