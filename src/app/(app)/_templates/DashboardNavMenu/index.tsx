'use client'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  AlignJustify,
  Library,
  LogOut,
  Megaphone,
  Navigation,
  Palette,
  Settings,
  Settings2,
  TriangleAlert,
} from 'lucide-react'
import NextImage from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import * as React from 'react'

import { toast } from 'sonner'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/app/(app)/_component/Drawer'
import { cn } from '@/lib/utils'
import { Spacing } from '@/utilities/local/css'
import { Button } from '../../_component/Button'
import ListButton from '../../_component/Button/variants/ListButton'
import NavigationMenu, {
  NavigationMainMenuCard,
  NavigationMenuCard,
  NavigationMenuLinkItem,
  NavigationMenuTriggerContent,
  NavigationMenuTriggerItem,
} from '../../_component/NavigationMenuV2'
import Text from '../../_component/Text'
import Title from '../../_component/Title'
import FlexContainer, { AlignItems, FlexDirection } from '../../_cssComp/FlexContainer'
import GridContainer, { ColSpan, GridItem } from '../../_cssComp/GridContainer'
import { DESKTOP_QUERY, useMediaQuery } from '../../_hooks/MediaQuery'
import { logoutUser } from '../../_localApi/users'
import { useAuthState } from '../../_state/authState'
import CostMenuIndicator from '../CostMenuIndicator'

function DashboardNavMenu() {
  const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
  const isDesktop = useMediaQuery(DESKTOP_QUERY)
  const queryClient = useQueryClient()
  const router = useRouter()

  const logoutMutation = useMutation({
    mutationFn: () => {
      return logoutUser()
    },
    onSuccess: async () => {
      useAuthState.setState({
        user: null,
        isLoggedIn: false,
        expiry: null,
      })
      queryClient.clear()
      indexedDB.deleteDatabase('keyval-store')
      router.push(HOST_URL)
    },
    onError: () => {
      toast.error('Logout failed', {
        description: 'Something went wrong trying to log you out. Please try again.',
        duration: 0,
        position: 'top-right',
        icon: <TriangleAlert />,
      })
    },
  })
  const handleLogout = async () => {
    await logoutMutation.mutateAsync()
  }
  return (
    <GridContainer className={cn(`grid-cols-4 border-b-2 border-black`, Spacing.ContentPadding)}>
      <GridItem colSpan={ColSpan.SPAN_1}>
        <Link href={HOST_URL}>
          <FlexContainer className={`w-full h-full gap-2`} align={AlignItems.CENTER} wrap={false}>
            <NextImage src="/media/logo/logo_v4.svg" width={50} height={50} alt="ColorAria logo" />
            <Title>ColorAria</Title>
          </FlexContainer>
        </Link>
      </GridItem>
      {isDesktop ? (
        <>
          <GridItem colSpan={ColSpan.SPAN_2}>
            <NavigationMenu>
              <NavigationMenuTriggerItem label="Generate Art" icon={<Palette />}>
                <NavigationMenuTriggerContent>
                  <GridContainer columns={2} className="w-[500px] gap-4 items-stretch">
                    <Link href={`${HOST_URL}/dashboard/image-generation`} className="h-full">
                      <NavigationMainMenuCard
                        title="Generate Art"
                        subtitle="Get started creating cool art now!"
                        icon={<Palette size={'24px'} />}
                        className="h-full"
                      />
                    </Link>
                    <FlexContainer direction={FlexDirection.COL}>
                      <Link href={`${HOST_URL}/blog/announcement`}>
                        <NavigationMenuCard
                          title="Announcements"
                          icon={<Megaphone size={'16px'} />}
                          subtitle="Check out the latest on our site!"
                        />
                      </Link>
                      <Link href={`${HOST_URL}/blog/tutorial`}>
                        <NavigationMenuCard
                          title="Tutorial"
                          icon={<Navigation size={'16px'} />}
                          subtitle="Learn how to use the app"
                        />
                      </Link>
                    </FlexContainer>
                  </GridContainer>
                </NavigationMenuTriggerContent>
              </NavigationMenuTriggerItem>
              <NavigationMenuLinkItem
                label="Image Library"
                link="dashboard/image-library"
                icon={<Library />}
              />
            </NavigationMenu>
          </GridItem>
          <GridItem colSpan={ColSpan.SPAN_1}>
            <CostMenuIndicator />
          </GridItem>
        </>
      ) : (
        <GridItem colSpan={ColSpan.SPAN_3} className="justify-self-end self-center">
          <Drawer direction="right">
            <DrawerTrigger asChild>
              <Button>
                <AlignJustify />
              </Button>
            </DrawerTrigger>
            <DrawerContent className="z-500">
              {/* <DrawerHeader>
                <DrawerTitle>ColorAria</DrawerTitle>
                <DrawerDescription>Welcome to ColorAria!</DrawerDescription>
              </DrawerHeader> */}
              <FlexContainer direction={FlexDirection.COL} className="w-full h-full mt-2 gap-4">
                <CostMenuIndicator allowHover={false} />
                <FlexContainer direction={FlexDirection.COL} className="w-full flex-1">
                  <Link
                    href={`${HOST_URL}/dashboard/image-generation`}
                    className="w-full border-t-2 border-black"
                  >
                    <ListButton className="w-full">
                      <Text variant="emphasis" size="base">
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <Palette /> Generate art
                        </FlexContainer>
                      </Text>
                    </ListButton>
                  </Link>
                  <Link
                    href={`${HOST_URL}/blog?category=Announcement`}
                    className="w-full border-t-2 border-black"
                  >
                    <ListButton className="w-full">
                      <Text variant="emphasis" size="base">
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <Megaphone /> Announcements
                        </FlexContainer>
                      </Text>
                    </ListButton>
                  </Link>
                  <Link
                    href={`${HOST_URL}/blog?category=Tutorial`}
                    className="w-full border-t-2 border-black"
                  >
                    <ListButton className="w-full">
                      <Text variant="emphasis" size="base">
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <Navigation /> Tutorial
                        </FlexContainer>
                      </Text>
                    </ListButton>
                  </Link>
                  <Link
                    href={`${HOST_URL}/dashboard/image-library`}
                    className="w-full border-t-2 border-black"
                  >
                    <ListButton className="w-full">
                      <Text variant="emphasis" size="base">
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <Library /> Image Library
                        </FlexContainer>
                      </Text>
                    </ListButton>
                  </Link>
                  <Link
                    href={`${HOST_URL}/dashboard/settings`}
                    className="w-full border-y-2 border-black"
                  >
                    <ListButton className="w-full">
                      <Text variant="emphasis" size="base">
                        <FlexContainer className="gap-2" align={AlignItems.CENTER}>
                          <Settings /> Settings
                        </FlexContainer>
                      </Text>
                    </ListButton>
                  </Link>
                </FlexContainer>
                <Button variant="secondary" className="p-4 w-[80%] mx-auto" onClick={handleLogout}>
                  <LogOut /> Logout
                </Button>
              </FlexContainer>
              <DrawerFooter>
                <DrawerClose asChild></DrawerClose>
              </DrawerFooter>
            </DrawerContent>
          </Drawer>
        </GridItem>
      )}
    </GridContainer>
  )
}

export default DashboardNavMenu

// const components: { title: string; href: string; description: string }[] = [
//   {
//     title: 'Alert Dialog',
//     href: 'https://ui.shadcn.com/docs/primitives/alert-dialog',
//     description:
//       'A modal dialog that interrupts the user with important content and expects a response.',
//   },
//   {
//     title: 'Hover Card',
//     href: 'https://ui.shadcn.com/docs/primitives/hover-card',
//     description: 'For sighted users to preview content available behind a link.',
//   },
//   {
//     title: 'Progress',
//     href: 'https://ui.shadcn.com/docs/primitives/progress',
//     description:
//       'Displays an indicator showing the completion progress of a task, typically displayed as a progress bar.',
//   },
//   {
//     title: 'Scroll-area',
//     href: 'https://ui.shadcn.com/docs/primitives/scroll-area',
//     description: 'Visually or semantically separates content.',
//   },
//   {
//     title: 'Tabs',
//     href: 'https://ui.shadcn.com/docs/primitives/tabs',
//     description:
//       'A set of layered sections of content—known as tab panels—that are displayed one at a time.',
//   },
//   {
//     title: 'Tooltip',
//     href: 'https://ui.shadcn.com/docs/primitives/tooltip',
//     description:
//       'A popup that displays information related to an element when the element receives keyboard focus or the mouse hovers over it.',
//   },
// ]

// export default function DashboardNavMenu() {
//   const HOST_URL = process.env.NEXT_PUBLIC_HOST || 'http://localhost:3000'
//   return (
//     <NavigationMenu className="z-5 m750:max-w-[300px]">
//       <NavigationMenuList className="m750:max-w-[300px]">
//         <NavigationMenuItem>
//           <Link href={`${HOST_URL}/dashboard`} legacyBehavior passHref>
//             <NavigationMenuLink className={navigationMenuTriggerStyle()}>
//               <span className="m750:max-w-[80px] m750:text-xs">Home</span>
//             </NavigationMenuLink>
//           </Link>
//         </NavigationMenuItem>
//         <NavigationMenuItem>
//           <NavigationMenuTrigger className="m750:max-w-[80px] m750:text-xs">
//             <span className="m750:hidden">Getting started</span>
//             <span className="hidden m750:inline">Home</span>
//           </NavigationMenuTrigger>
//           <NavigationMenuContent>
//             <ul className="grid w-[500px] gap-3 p-6 lg:grid-cols-[.75fr_1fr] m750:w-[300px]">
//               <li className="row-span-3">
//                 <NavigationMenuLink asChild>
//                   <Link
//                     className="from-muted/50 to-muted flex h-full w-full select-none flex-col justify-end rounded-md bg-linear-to-b p-6 no-underline outline-hidden focus:shadow-md"
//                     href={`${HOST_URL}/dashboard/image-generation`}
//                   >
//                     <Palette color="white" />
//                     <div className="mb-2 mt-4 text-lg font-heading">Generate Coloring Page</div>
//                     <p className="text-sm font-base leading-tight">
//                       Get started by creating your first page!
//                     </p>
//                   </Link>
//                 </NavigationMenuLink>
//               </li>
//               <Link href={`${HOST_URL}/dashboard/image-generation`}>
//                 <ListItem title="Tutorial">Access our tutorials here!</ListItem>
//               </Link>
//             </ul>
//           </NavigationMenuContent>
//         </NavigationMenuItem>
//         <NavigationMenuItem>
//           <Link href={`${HOST_URL}/dashboard/settings`} legacyBehavior passHref>
//             <NavigationMenuLink className={navigationMenuTriggerStyle()}>
//               <span className="m750:max-w-[80px] m750:text-xs">Settings</span>
//             </NavigationMenuLink>
//           </Link>
//         </NavigationMenuItem>
//       </NavigationMenuList>
//     </NavigationMenu>
//   )
// }

// const ListItem = React.forwardRef<React.ElementRef<'div'>, React.ComponentPropsWithoutRef<'div'>>(
//   ({ className, title, children, ...props }, ref) => {
//     return (
//       <li>
//         <NavigationMenuLink asChild>
//           <div
//             ref={ref}
//             className={cn(
//               'hover:bg-accent block text-text select-none space-y-1 rounded-base border-2 border-transparent p-3 leading-none no-underline outline-hidden transition-colors hover:border-border dark:hover:border-dark-border',
//               className,
//             )}
//             {...props}
//           >
//             <div className="text-base font-heading leading-none">{title}</div>
//             <p className="text-muted-foreground font-base line-clamp-2 text-sm leading-snug">
//               {children}
//             </p>
//           </div>
//         </NavigationMenuLink>
//       </li>
//     )
//   },
// )
// ListItem.displayName = 'ListItem'
