import Link from 'next/link'
import { Spacing } from '@/utilities/local/css'
import Text from '../../_component/Text'
import GridContainer, { GridItem } from '../../_cssComp/GridContainer'

function MainFooter() {
  return (
    <GridContainer
      className={`w-full ${Spacing.ContentPadding} flex flex-col lg:flex-row lg:justify-between lg:items-center gap-2`}
    >
      <div className="flex justify-center lg:justify-start gap-2">
        <Text>Copyright ColorAria &copy; 2025 | Made with ❤️ </Text>
      </div>
      <div className="flex justify-center lg:justify-end gap-2">
        <Link href="/privacy-policy" className="text-gray-800 hover:text-gray-500 underline">
          Privacy Policy
        </Link>
        <span>|</span>
        <Link href="/terms-of-service" className="text-gray-800 hover:text-gray-500 underline">
          Terms of Service
        </Link>
      </div>
    </GridContainer>
  )
}

export default MainFooter
