import PagesDAO from '../../dao/PagesDAO'
import { NotFound } from '../../exception'

class PagesService {
  private pageDAO: PagesDAO
  constructor(pageDAO: PagesDAO) {
    this.pageDAO = pageDAO
  }

  async getPageBySlug(slug: string, onlyPublished = true) {
    const page = await this.pageDAO.getPageBySlug(slug, onlyPublished)
    if (!page || page.docs.length === 0) {
      throw new NotFound('Page not found') // Prevent caching null
    }
    return page.docs[0]
  }
}

export default PagesService
