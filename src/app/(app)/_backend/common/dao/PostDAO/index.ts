import { Options } from '@payloadcms/ui'
import { SelectFromCollectionSlug } from 'node_modules/payload/dist/collections/config/types'
import { BasePayload, CollectionSlug, Sort, Where } from 'payload'
import { Category, Post } from '@/payload-types'

class PostDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async getAllPages(currentPage: number, pageSize: number, where: Where | undefined = undefined) {
    const pages = await this.payload.find({
      collection: 'posts',
      pagination: true,
      page: currentPage,
      limit: pageSize,
      where: where,
    })

    return pages
  }

  async getAllPostsWithSlugOnly() {
    const posts = await this.payload.find({
      collection: 'posts',
      pagination: false,
      select: {
        slug: true,
        category: true,
        updatedAt: true,
      },
      where: {
        publishDate: {
          less_than_equal: new Date().toISOString(),
        },
      },
    })
    return posts
  }

  async getPostBySlug(slug: string, onlyPublished: boolean) {
    const where: Record<string, any> = {
      and: [
        {
          slug: {
            equals: slug,
          },
        },
      ],
    }

    if (onlyPublished) {
      where.and.push({
        publishDate: {
          less_than_equal: new Date().toISOString(),
        },
      })
    }

    console.log(where)

    const post = await this.payload.find({
      collection: 'posts',
      where,
    })

    return post
  }

  async getPostByCategoryAndSlug(category: string, slug: string, onlyPublished: boolean) {
    const where: Record<string, any> = {
      and: [
        {
          'category.slug': {
            equal: category,
          },
        },
        {
          slug: {
            equals: slug,
          },
        },
      ],
    }

    if (onlyPublished) {
      where.and.push({
        publishDate: {
          less_than_equal: new Date().toISOString(),
        },
      })
    }

    const post = await this.payload.find({
      collection: 'posts',
      where,
    })

    return post
  }

  async getPostByCategories(category: Category[]) {
    const post = await this.payload.find({
      collection: 'posts',
      where: {
        category: {
          in: category,
        },
      },
    })

    return post
  }

  async getPostsDynamically(where: Where, sort: Sort | undefined = undefined) {
    const post = await this.payload.find({
      collection: 'posts',
      where,
      sort: sort,
    })

    return post
  }
}

export default PostDAO
