import { BasePayload } from 'payload'
import { SubscriptionRecord } from '@/payload-types'

class SubscriptionRecordsDAO {
  private payload: BasePayload

  constructor(payload: BasePayload) {
    this.payload = payload
  }

  async addSubscription(
    stripeSubscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    stripeCustomerId: string | null = null,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string | null,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
  ) {
    const result = await this.payload.create({
      collection: 'subscription-records',
      data: {
        stripeSubscriptionId: stripeSubscriptionId,
        user: userId,
        subscriptionPlan: subscriptionPlanId,
        stripeCustomerId: stripeCustomerId,
        status: status as SubscriptionRecord['status'],
        currentPeriodStart: new Date(Number(currentPeriodStart) * 1000).toISOString(),
        currentPeriodEnd: new Date(Number(currentPeriodEnd) * 1000).toISOString(),
        stripeComment: stripeComment,
        cancelAt: cancelAt ? new Date(Number(cancelAt) * 1000).toISOString() : null,
        canceledAt: canceledAt ? new Date(Number(canceledAt) * 1000).toISOString() : null,
      },
    })

    return result
  }

  async retrieveSubscriptionRecordBySubscriptionId(
    subscriptionId: string,
  ): Promise<SubscriptionRecord | null> {
    const result = await this.payload.find({
      collection: 'subscription-records',
      depth: 2,
      where: {
        stripeSubscriptionId: {
          equals: subscriptionId,
        },
      },
    })

    return result.docs[0]
  }

  async retrieveSubscriptionRecordById(id: string): Promise<SubscriptionRecord | null> {
    const result = await this.payload.findByID({
      collection: 'subscription-records',
      id: id,
    })

    return result
  }

  async updateSubscriptionRecord(
    id: number,
    stripeSubscriptionId: string | null = null,
    userId: string,
    subscriptionPlanId: string,
    stripeCustomerId: string | null = null,
    status: string,
    currentPeriodStart: number,
    currentPeriodEnd: number,
    stripeComment: string | null,
    cancelAt: number | null = null,
    canceledAt: number | null = null,
    lastCreditRelease: string | null = null,
  ) {
    const result = await this.payload.update({
      collection: 'subscription-records',
      id: id,
      data: {
        stripeSubscriptionId: stripeSubscriptionId,
        user: userId,
        subscriptionPlan: subscriptionPlanId,
        stripeCustomerId: stripeCustomerId,
        status: status as SubscriptionRecord['status'],
        currentPeriodStart: new Date(Number(currentPeriodStart) * 1000).toISOString(),
        currentPeriodEnd: new Date(Number(currentPeriodEnd) * 1000).toISOString(),
        stripeComment: stripeComment,
        cancelAt: cancelAt ? new Date(Number(cancelAt) * 1000).toISOString() : null,
        canceledAt: canceledAt ? new Date(Number(canceledAt) * 1000).toISOString() : null,
        lastCreditRelease: lastCreditRelease,
      },
    })

    return result
  }
  async genericUpdateSubscriptionRecord(id: number, data: Partial<SubscriptionRecord>) {
    const userInfo = await this.payload.update({
      collection: 'subscription-records',
      id: id,
      data: data,
    })

    return userInfo
  }

  async retrieveSubscriptionRecordByUserId(userId: string) {
    const result = await this.payload.find({
      collection: 'subscription-records',
      depth: 1,
      where: {
        user: {
          equals: userId,
        },
      },
    })
    return result.totalDocs > 0 ? result.docs[0] : null
  }
}

export default SubscriptionRecordsDAO
