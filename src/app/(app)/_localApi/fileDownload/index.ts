import { FileDownloadResponse } from '../../api/file-download/route'
import { apiFetch, ApiResponse } from '../util'

export async function fetchPresignedUrl(
  filename: string,
): Promise<ApiResponse<FileDownloadResponse>> {
  const headers = {
    method: 'POST',
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ filename }),
  } as RequestInit
  const response = await apiFetch<FileDownloadResponse>(`/api/file-download`, headers)

  return response
}
